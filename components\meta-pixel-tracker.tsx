"use client"

import { useEffect } from "react"
import { usePathname, useSearchParams } from "next/navigation"
import { trackPageView, initMetaPixel } from "@/lib/meta-pixel"

export default function MetaPixelTracker() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    // Initialize Meta Pixel on first load
    initMetaPixel()
  }, [])

  useEffect(() => {
    // Track page view on route change
    trackPageView()

    // Debug log for development
    if (process.env.NODE_ENV === 'development') {
      console.log('Meta Pixel: PageView tracked for', pathname)
    }
  }, [pathname, searchParams])

  return null
}
